#!/usr/bin/env python3
"""
LinkCID BLE broadcasting test script
Based on link_protocol.cc implementation

This script implements real BLE broadcasting for LinkCID using:
- Manufacturer ID: 0xFFFF
- BLE Extended Advertising
- Manufacturer data format: [0xFF, 0xFF, LinkCID_string...]
"""

import struct
import time
import random
import subprocess
import sys

try:
    from bleak import BleakScanner
    import asyncio
    BLEAK_AVAILABLE = True
except ImportError:
    BLEAK_AVAILABLE = False
    print("Warning: bleak not available. Install with: pip install bleak")

try:
    import winrt.windows.devices.bluetooth as winrt_bluetooth
    import winrt.windows.devices.bluetooth.advertisement as winrt_adv
    import winrt.windows.storage.streams as winrt_streams
    WINRT_AVAILABLE = True
except ImportError:
    WINRT_AVAILABLE = False
    print("Warning: winrt not available. Install with: pip install winrt")

try:
    import winsdk.windows.devices.bluetooth as winsdk_bluetooth
    import winsdk.windows.devices.bluetooth.advertisement as winsdk_adv
    WINSDK_AVAILABLE = True
except ImportError:
    WINSDK_AVAILABLE = False
    print("Warning: winsdk not available. Install with: pip install winsdk")


def broad_linkcid(linkcid, device_name="linkpe"):
    """
    Real BLE LinkCID broadcasting functionality from link_protocol.cc

    Args:
        linkcid (str): The LinkCID string to broadcast
        device_name (str): Device name to broadcast (default: "linkpe" from link_protocol.cc)

    Returns:
        dict: Result of the broadcast operation
    """

    # Constants from link_protocol.cc
    MANUFACTURER_ID = 0xFFFF  # LinkCID broadcast uses 0xFFFF
    MAX_LINKCID_LENGTH = 1600
    BROADCAST_DURATION_MS = 200
    RANDOM_DELAY_MAX_MS = 20

    print(f"[BROADCAST] Starting real BLE LinkCID broadcast: {linkcid}")
    print(f"[BROADCAST] Device name: {device_name}")

    # Validate LinkCID (mirrors validation logic from link_protocol.cc)
    if not linkcid:
        return {"success": False, "message": "LinkCID cannot be empty"}

    if len(linkcid) > MAX_LINKCID_LENGTH:
        return {"success": False, "message": f"LinkCID too long: {len(linkcid)} bytes (max {MAX_LINKCID_LENGTH})"}

    # Check for valid IPFS hash format (Qm... or baf...)
    if len(linkcid) < 10 or not (linkcid.startswith("Qm") or linkcid.startswith("baf")):
        return {"success": False, "message": "Invalid LinkCID format (must start with 'Qm' or 'baf' and be at least 10 chars)"}

    # Validate device name
    if not device_name:
        return {"success": False, "message": "Device name cannot be empty (required for link protocol filtering)"}

    # Try different BLE broadcasting methods for Windows
    try:
        # Method 1: Try using Windows Runtime (WinRT) BLE advertising
        if WINRT_AVAILABLE:
            result = broadcast_with_winrt(linkcid, MANUFACTURER_ID, BROADCAST_DURATION_MS, RANDOM_DELAY_MAX_MS, device_name)
            if result["success"]:
                return result

        # Method 2: Try using Windows SDK
        if WINSDK_AVAILABLE:
            result = broadcast_with_winsdk(linkcid, MANUFACTURER_ID, BROADCAST_DURATION_MS, RANDOM_DELAY_MAX_MS, device_name)
            if result["success"]:
                return result

        # Method 3: Try using Bleak (cross-platform, works on Windows)
        if BLEAK_AVAILABLE:
            result = broadcast_with_bleak(linkcid, MANUFACTURER_ID, BROADCAST_DURATION_MS, RANDOM_DELAY_MAX_MS, device_name)
            if result["success"]:
                return result

        # Method 4: Try PowerShell commands (Windows specific)
        result = broadcast_with_powershell(linkcid, MANUFACTURER_ID, BROADCAST_DURATION_MS, RANDOM_DELAY_MAX_MS, device_name)
        if result["success"]:
            return result

        return {"success": False, "message": "No available Windows BLE broadcasting method found"}

    except Exception as e:
        return {"success": False, "message": f"Broadcast failed: {str(e)}"}


# Linux/Unix specific functions removed for Windows compatibility
# These functions (bluetoothctl, hcitool) are not available on Windows


def broadcast_with_winrt(linkcid, manufacturer_id, duration_ms, random_delay_max, device_name="linkpe"):
    """Broadcast using Windows Runtime (WinRT) BLE advertising"""
    try:
        print("[BROADCAST] Trying Windows Runtime (WinRT) method...")

        # Create advertising data
        linkcid_bytes = linkcid.encode('utf-8')
        device_name_bytes = device_name.encode('utf-8')

        # Random delay
        random_delay = random.randint(0, random_delay_max)
        total_duration = duration_ms + random_delay

        print(f"[BROADCAST] Device name: {device_name}")
        print(f"[BROADCAST] Broadcasting for {total_duration}ms")

        # Create BLE advertisement publisher
        publisher = winrt_adv.BluetoothLEAdvertisementPublisher()

        # Set advertisement data
        advertisement = publisher.advertisement

        # Set device name
        advertisement.local_name = device_name

        # Create manufacturer data
        mfg_data = winrt_adv.BluetoothLEManufacturerData()
        mfg_data.company_id = manufacturer_id

        # Create data writer for manufacturer data
        writer = winrt_streams.DataWriter()
        writer.write_bytes(list(linkcid_bytes))
        mfg_data.data = writer.detach_buffer()

        # Add manufacturer data to advertisement
        advertisement.manufacturer_data.append(mfg_data)

        # Set advertisement flags
        advertisement.flags = winrt_adv.BluetoothLEAdvertisementFlags.GENERAL_DISCOVERABLE_MODE

        start_time = time.time()

        # Start advertising
        publisher.start()

        # Wait for broadcast duration
        time.sleep(total_duration / 1000.0)

        # Stop advertising
        publisher.stop()

        end_time = time.time()
        actual_duration = int((end_time - start_time) * 1000)

        return {
            "success": True,
            "message": "LinkCID broadcast completed with WinRT",
            "method": "winrt",
            "device_name": device_name,
            "linkcid": linkcid,
            "duration_ms": actual_duration,
            "manufacturer_id": f"0x{manufacturer_id:04X}"
        }

    except Exception as e:
        return {"success": False, "message": f"WinRT error: {str(e)}"}


def broadcast_with_winsdk(linkcid, manufacturer_id, duration_ms, random_delay_max, device_name="linkpe"):
    """Broadcast using Windows SDK"""
    try:
        print("[BROADCAST] Trying Windows SDK method...")

        # Similar implementation to WinRT but using winsdk
        # This is a placeholder for now
        return {"success": False, "message": "Windows SDK BLE advertising not fully implemented"}

    except Exception as e:
        return {"success": False, "message": f"Windows SDK error: {str(e)}"}


def broadcast_with_bleak(linkcid, manufacturer_id, duration_ms, random_delay_max, device_name="linkpe"):
    """Broadcast using Bleak library (cross-platform, works on Windows)"""
    try:
        print("[BROADCAST] Trying Bleak method...")

        # Note: Bleak is primarily for scanning and connecting, not advertising
        # BLE advertising on Windows through Bleak is limited
        return {"success": False, "message": "Bleak does not support BLE advertising on Windows"}

    except Exception as e:
        return {"success": False, "message": f"Bleak error: {str(e)}"}


def broadcast_with_powershell(linkcid, manufacturer_id, duration_ms, random_delay_max, device_name="linkpe"):
    """Broadcast using PowerShell commands (Windows specific)"""
    try:
        print("[BROADCAST] Trying PowerShell method...")

        # Create advertising data
        linkcid_bytes = linkcid.encode('utf-8')

        # Random delay
        random_delay = random.randint(0, random_delay_max)
        total_duration = duration_ms + random_delay

        print(f"[BROADCAST] Device name: {device_name}")
        print(f"[BROADCAST] Broadcasting for {total_duration}ms")

        # PowerShell script to create BLE advertisement
        powershell_script = f'''
Add-Type -AssemblyName System.Runtime.WindowsRuntime
$asTaskGeneric = ([System.WindowsRuntimeSystemExtensions].GetMethods() | ? {{ $_.Name -eq 'AsTask' -and $_.GetParameters().Count -eq 1 -and $_.GetParameters()[0].ParameterType.Name -eq 'IAsyncOperation`1' }})[0]

# Create BLE advertisement publisher
$publisher = [Windows.Devices.Bluetooth.Advertisement.BluetoothLEAdvertisementPublisher]::new()

# Set device name
$publisher.Advertisement.LocalName = "{device_name}"

# Create manufacturer data
$mfgData = [Windows.Devices.Bluetooth.Advertisement.BluetoothLEManufacturerData]::new()
$mfgData.CompanyId = {manufacturer_id}

# Convert LinkCID to bytes
$linkcidBytes = [System.Text.Encoding]::UTF8.GetBytes("{linkcid}")
$writer = [Windows.Storage.Streams.DataWriter]::new()
$writer.WriteBytes($linkcidBytes)
$mfgData.Data = $writer.DetachBuffer()

# Add manufacturer data
$publisher.Advertisement.ManufacturerData.Add($mfgData)

# Set flags
$publisher.Advertisement.Flags = [Windows.Devices.Bluetooth.Advertisement.BluetoothLEAdvertisementFlags]::GeneralDiscoverableMode

# Start advertising
$publisher.Start()

# Wait for duration
Start-Sleep -Milliseconds {total_duration}

# Stop advertising
$publisher.Stop()

Write-Output "Broadcast completed"
'''

        start_time = time.time()

        # Execute PowerShell script
        result = subprocess.run(
            ["powershell", "-Command", powershell_script],
            capture_output=True,
            text=True,
            timeout=total_duration/1000.0 + 5
        )

        end_time = time.time()
        actual_duration = int((end_time - start_time) * 1000)

        if result.returncode == 0:
            return {
                "success": True,
                "message": "LinkCID broadcast completed with PowerShell",
                "method": "powershell",
                "device_name": device_name,
                "linkcid": linkcid,
                "duration_ms": actual_duration,
                "manufacturer_id": f"0x{manufacturer_id:04X}"
            }
        else:
            return {"success": False, "message": f"PowerShell error: {result.stderr}"}

    except subprocess.TimeoutExpired:
        return {"success": False, "message": "PowerShell command timeout"}
    except FileNotFoundError:
        return {"success": False, "message": "PowerShell not found"}
    except Exception as e:
        return {"success": False, "message": f"PowerShell error: {str(e)}"}


def board_linkcid_forever(linkcid, device_name="linkpe"):
    """
    Continuously broadcast LinkCID forever (until interrupted)
    Mirrors the link_cycle_task behavior from link_protocol.cc

    Args:
        linkcid (str): The LinkCID string to broadcast continuously
        device_name (str): Device name to broadcast (default: "linkpe" from link_protocol.cc)

    Returns:
        dict: Result of the broadcast operation
    """

    # Constants from link_protocol.cc
    MANUFACTURER_ID = 0xFFFF
    BROADCAST_DURATION_MS = 200
    SCAN_DURATION_MS = 200
    SLEEP_DURATION_MS = 200
    RANDOM_DELAY_MAX_MS = 20

    print(f"[FOREVER] Starting continuous LinkCID broadcast: {linkcid}")
    print("[FOREVER] Press Ctrl+C to stop...")

    # Validate LinkCID first
    if not linkcid:
        return {"success": False, "message": "LinkCID cannot be empty"}

    if len(linkcid) > 1600:
        return {"success": False, "message": f"LinkCID too long: {len(linkcid)} bytes (max 1600)"}

    if len(linkcid) < 10 or not (linkcid.startswith("Qm") or linkcid.startswith("baf")):
        return {"success": False, "message": "Invalid LinkCID format"}

    cycle_count = 0
    start_time = time.time()

    try:
        while True:
            cycle_count += 1
            cycle_start = time.time()

            print(f"\n[FOREVER] === Cycle {cycle_count} ===")

            # Phase 1: Broadcast for 200ms + random delay (0-20ms)
            random_delay = random.randint(0, RANDOM_DELAY_MAX_MS)
            broadcast_duration = BROADCAST_DURATION_MS + random_delay

            print(f"[FOREVER] Phase 1: Broadcasting for {broadcast_duration}ms")
            broadcast_result = broadcast_single_cycle(linkcid, broadcast_duration, device_name)

            # Phase 2: Scan for 200ms (simulated - would need real BLE scanning)
            print(f"[FOREVER] Phase 2: Scanning for {SCAN_DURATION_MS}ms")
            time.sleep(SCAN_DURATION_MS / 1000.0)

            # Phase 3: Sleep for 200ms
            print(f"[FOREVER] Phase 3: Sleeping for {SLEEP_DURATION_MS}ms")
            time.sleep(SLEEP_DURATION_MS / 1000.0)

            cycle_end = time.time()
            cycle_duration = int((cycle_end - cycle_start) * 1000)
            total_runtime = int((cycle_end - start_time) * 1000)

            print(f"[FOREVER] Cycle {cycle_count} completed in {cycle_duration}ms (total runtime: {total_runtime}ms)")

    except KeyboardInterrupt:
        end_time = time.time()
        total_duration = int((end_time - start_time) * 1000)

        print(f"\n[FOREVER] Broadcast stopped by user")
        print(f"[FOREVER] Total cycles: {cycle_count}")
        print(f"[FOREVER] Total runtime: {total_duration}ms")

        return {
            "success": True,
            "message": "Continuous broadcast stopped by user",
            "linkcid": linkcid,
            "total_cycles": cycle_count,
            "total_duration_ms": total_duration
        }

    except Exception as e:
        return {"success": False, "message": f"Forever broadcast failed: {str(e)}"}


def board_linkcid_list(linkcid_list, cycles=1, device_name="linkpe"):
    """
    Broadcast a list of LinkCIDs in sequence
    Each LinkCID is broadcast for one complete cycle before moving to the next

    Args:
        linkcid_list (list): List of LinkCID strings to broadcast
        cycles (int): Number of complete cycles through the list (default: 1)
        device_name (str): Device name to broadcast (default: "linkpe" from link_protocol.cc)

    Returns:
        dict: Result of the broadcast operation with details for each LinkCID
    """

    print(f"[LIST] Starting sequential LinkCID broadcast")
    print(f"[LIST] LinkCIDs: {len(linkcid_list)}, Cycles: {cycles}")

    if not linkcid_list:
        return {"success": False, "message": "LinkCID list cannot be empty"}

    if not isinstance(linkcid_list, list):
        return {"success": False, "message": "linkcid_list must be a list"}

    # Validate all LinkCIDs first
    for i, linkcid in enumerate(linkcid_list):
        if not linkcid:
            return {"success": False, "message": f"LinkCID at index {i} is empty"}

        if len(linkcid) > 1600:
            return {"success": False, "message": f"LinkCID at index {i} too long: {len(linkcid)} bytes"}

        if len(linkcid) < 10 or not (linkcid.startswith("Qm") or linkcid.startswith("baf")):
            return {"success": False, "message": f"Invalid LinkCID format at index {i}: {linkcid}"}

    results = []
    total_broadcasts = 0
    start_time = time.time()

    try:
        for cycle in range(cycles):
            print(f"\n[LIST] === Cycle {cycle + 1}/{cycles} ===")

            for i, linkcid in enumerate(linkcid_list):
                print(f"\n[LIST] Broadcasting LinkCID {i + 1}/{len(linkcid_list)}: {linkcid}")

                # Broadcast this LinkCID for one cycle (600ms total: 200ms broadcast + 200ms scan + 200ms sleep)
                cycle_start = time.time()

                # Phase 1: Broadcast
                random_delay = random.randint(0, 20)
                broadcast_duration = 200 + random_delay
                broadcast_result = broadcast_single_cycle(linkcid, broadcast_duration, device_name)

                # Phase 2: Scan (simulated)
                time.sleep(0.2)

                # Phase 3: Sleep
                time.sleep(0.2)

                cycle_end = time.time()
                cycle_duration = int((cycle_end - cycle_start) * 1000)

                result_entry = {
                    "linkcid": linkcid,
                    "cycle": cycle + 1,
                    "index": i,
                    "duration_ms": cycle_duration,
                    "broadcast_result": broadcast_result
                }

                results.append(result_entry)
                total_broadcasts += 1

                print(f"[LIST] LinkCID {i + 1} completed in {cycle_duration}ms")

        end_time = time.time()
        total_duration = int((end_time - start_time) * 1000)

        return {
            "success": True,
            "message": "Sequential LinkCID broadcast completed",
            "total_linkcids": len(linkcid_list),
            "total_cycles": cycles,
            "total_broadcasts": total_broadcasts,
            "total_duration_ms": total_duration,
            "results": results
        }

    except KeyboardInterrupt:
        end_time = time.time()
        total_duration = int((end_time - start_time) * 1000)

        print(f"\n[LIST] Broadcast stopped by user")

        return {
            "success": False,
            "message": "Sequential broadcast stopped by user",
            "total_broadcasts": total_broadcasts,
            "total_duration_ms": total_duration,
            "partial_results": results
        }

    except Exception as e:
        return {"success": False, "message": f"List broadcast failed: {str(e)}"}


def broadcast_single_cycle(linkcid, duration_ms, device_name="linkpe"):
    """
    Helper function to broadcast a single LinkCID for a specified duration

    Args:
        linkcid (str): LinkCID to broadcast
        duration_ms (int): Duration in milliseconds
        device_name (str): Device name to broadcast

    Returns:
        dict: Broadcast result
    """
    try:
        # Try Windows Runtime first
        if WINRT_AVAILABLE:
            result = broadcast_with_winrt(linkcid, 0xFFFF, duration_ms, 0, device_name)
            if result["success"]:
                return result

        # Try PowerShell as fallback
        result = broadcast_with_powershell(linkcid, 0xFFFF, duration_ms, 0, device_name)
        if result["success"]:
            return result

        # If no real broadcast available, simulate
        print(f"[CYCLE] Simulating broadcast for {duration_ms}ms (name: {device_name})")
        time.sleep(duration_ms / 1000.0)

        return {
            "success": True,
            "message": "Simulated broadcast completed",
            "method": "simulation",
            "device_name": device_name,
            "linkcid": linkcid,
            "duration_ms": duration_ms
        }

    except Exception as e:
        return {"success": False, "message": f"Single cycle broadcast failed: {str(e)}"}


def extract_linkcid_from_advertising_data(adv_data):
    """
    Extract LinkCID from BLE advertising data
    Mirrors extract_linkcid_from_mfg_data function from link_protocol.cc

    Args:
        adv_data (bytes): BLE advertising data

    Returns:
        str: Extracted LinkCID or empty string if not found
    """
    MANUFACTURER_ID = 0xFFFF

    # Parse advertising data to find manufacturer data
    i = 0
    while i < len(adv_data):
        if i + 1 >= len(adv_data):
            break

        length = adv_data[i]
        if length == 0 or i + length + 1 > len(adv_data):
            break

        ad_type = adv_data[i + 1]

        # Look for manufacturer data (type 0xFF)
        if ad_type == 0xFF and length >= 3:
            # Extract manufacturer ID (little-endian)
            if i + 4 <= len(adv_data):
                mfg_id = struct.unpack('<H', adv_data[i + 2:i + 4])[0]

                # Check if it's LinkCID manufacturer ID
                if mfg_id == MANUFACTURER_ID:
                    # Extract LinkCID string
                    linkcid_data = adv_data[i + 4:i + 1 + length]
                    return linkcid_data.decode('utf-8', errors='ignore')

        i += length + 1

    return ""


# Test examples
if __name__ == '__main__':
    # print("=== LinkCID Broadcast Test (Windows) ===\n")

    # # Check available Windows BLE libraries
    # print("Available Windows BLE libraries:")
    # print(f"  - WinRT: {'✓' if WINRT_AVAILABLE else '✗'}")
    # print(f"  - Windows SDK: {'✓' if WINSDK_AVAILABLE else '✗'}")
    # print(f"  - Bleak: {'✓' if BLEAK_AVAILABLE else '✗'}")
    # print(f"  - PowerShell: Available on Windows")
    # print()

    # if not WINRT_AVAILABLE:
    #     print("To install WinRT support: pip install winrt")
    # if not BLEAK_AVAILABLE:
    #     print("To install Bleak support: pip install bleak")
    # print()

    # # Test with default LinkCID from link_protocol.cc
    default_linkcid = "QmbfipMRwKAdB4V8dR4TTn9jYAuviLKWckDuEPtWFH6y9t"

    # print("Test 1: Single LinkCID broadcast with device name")
    # result = broad_linkcid(default_linkcid, device_name="linkpe")
    # print(f"Result: {result}\n")

    # print("Test 2: LinkCID list broadcast with device name")
    # test_linkcids = [
    #     "QmbfipMRwKAdB4V8dR4TTn9jYAuviLKWckDuEPtWFH6y9t",
    #     "QmTestLinkCID123456789",
    #     "bafybeigdyrzt5sfp7udm7hu76uh7y26nf3efuylqabf3oclgtqy55fbzdi"
    # ]
    # result = board_linkcid_list(test_linkcids, cycles=2, device_name="linkpe")
    # print(f"Result: {result}\n")

    #Uncomment the line below to test continuous broadcasting (Ctrl+C to stop)
    print("Test 3: Continuous LinkCID broadcast (Ctrl+C to stop)")
    result = board_linkcid_forever(default_linkcid, device_name="linkpe")
    print(f"Result: {result}\n")

    print("=== Test completed ===")
    print("Note: All broadcasts include device name 'linkpe' and manufacturer ID 0xFFFF")
    print("This ensures compatibility with link_protocol.cc filtering requirements.")
    print()
    print("Windows BLE Broadcasting Methods:")
    print("1. WinRT (Windows Runtime) - Recommended for Windows 10/11")
    print("2. PowerShell - Fallback method using Windows PowerShell")
    print("3. Simulation - If no real BLE broadcasting is available")
    print()
    print("To test continuous broadcasting, uncomment the forever test and run again.")